#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import jwt
import json

# 测试JWT
test_token = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCJ9.YEfuSy_rl2g4_9rbSd4A34qtatSWhOBfMe-Rzw0c3McGJQjMBIUs_9VWxEx8inrDU5asAtVTYTyl0m6bVmYfzw"

print("解码JWT token:")
print(f"Token: {test_token}")
print()

# 不验证签名，只解码payload
try:
    # 解码header
    header = jwt.get_unverified_header(test_token)
    print(f"Header: {json.dumps(header, indent=2)}")
    
    # 解码payload (不验证签名)
    payload = jwt.decode(test_token, options={"verify_signature": False})
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    # 检查必需字段
    if 'login_user_key' in payload:
        print(f"✅ 找到 login_user_key: {payload['login_user_key']}")
    else:
        print("❌ 缺少 login_user_key 字段")
        print(f"实际字段: {list(payload.keys())}")
    
except Exception as e:
    print(f"❌ 解码失败: {e}")

print("\n" + "="*50)
print("重新生成正确的JWT:")

# 重新生成JWT
secret = "abcdefghijklfastbeesmartrstuvwxyz"
session_id = "457b5f2c-6482-47c3-b22c-0cf6a5cf3508"

# 正确的payload格式 (添加exp字段和username)
import time
correct_payload = {
    "login_user_key": session_id,
    "username": "admin",  # 从LoginUser数据中获取的用户名
    "exp": int(time.time()) + 86400  # 24小时后过期
}

new_token = jwt.encode(correct_payload, secret, algorithm="HS512")
print(f"新Token: {new_token}")

# 验证新token
try:
    decoded = jwt.decode(new_token, options={"verify_signature": False})
    print(f"新Token解码: {json.dumps(decoded, indent=2)}")
except Exception as e:
    print(f"新Token解码失败: {e}")
