#!/usr/bin/env python3
"""
检查Redis中的所有数据
"""
import asyncio
from backend.database.redis import redis_client

async def check_all_redis_data():
    """检查Redis中的所有数据"""
    print("=" * 60)
    print("Redis 所有数据检查")
    print("=" * 60)
    
    try:
        # 获取数据库信息
        info = await redis_client.info()
        print(f"Redis版本: {info.get('redis_version', 'unknown')}")
        print(f"当前数据库: {info.get('db0', 'unknown')}")
        
        # 获取所有keys
        keys = []
        async for key in redis_client.scan_iter():
            keys.append(key)
        
        print(f"\n总共找到 {len(keys)} 个keys")
        
        if keys:
            print("\n前20个keys:")
            for i, key in enumerate(keys[:20], 1):
                try:
                    # 获取key的类型
                    key_type = await redis_client.type(key)
                    print(f"{i:2d}. {key} ({key_type})")
                    
                    # 如果是string类型，显示值的长度
                    if key_type == 'string':
                        value = await redis_client.get(key)
                        if value:
                            print(f"    值长度: {len(value)} 字符")
                            if len(value) < 100:
                                print(f"    内容: {value}")
                            else:
                                print(f"    内容预览: {value[:50]}...")
                except Exception as e:
                    print(f"    错误: {e}")
        else:
            print("Redis数据库为空")
            
        # 搜索可能的token相关keys
        print(f"\n--- 搜索token相关keys ---")
        token_patterns = ['*token*', '*login*', '*user*', '*session*', '*auth*']
        
        for pattern in token_patterns:
            pattern_keys = []
            async for key in redis_client.scan_iter(match=pattern):
                pattern_keys.append(key)
            
            if pattern_keys:
                print(f"{pattern}: 找到 {len(pattern_keys)} 个")
                for key in pattern_keys[:5]:  # 只显示前5个
                    print(f"  - {key}")
            else:
                print(f"{pattern}: 未找到")
                
    except Exception as e:
        print(f"检查Redis数据时出错: {e}")

if __name__ == "__main__":
    asyncio.run(check_all_redis_data())
