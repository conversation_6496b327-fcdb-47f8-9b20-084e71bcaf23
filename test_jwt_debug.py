#!/usr/bin/env python3
"""
JWT Token 验证调试脚本
"""
import asyncio
import jwt
from backend.common.security.iot_adapter import iot_adapter
from backend.database.redis import redis_client

async def test_jwt_token():
    """测试JWT token验证过程"""
    token = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjY2MmU0MDEwLTk5MGItNDFjNy05YTkxLTczMmJmNWQ0Mzg2YiJ9.9mbJA3hUDgVMxcbtYJuMd2Ni69CgefIfcLjeG63_xOvN0Zc196TcHBaipBZWkMDDknXT1dcaAbE5YbrbMGokzA"

    print("=" * 60)
    print("JWT Token 验证调试")
    print("=" * 60)

    # 1. 检查IoT适配器配置
    print(f"IoT集成启用: {iot_adapter.is_enabled()}")
    print(f"IoT密钥: {iot_adapter.secret_key}")
    print(f"IoT算法: {iot_adapter.algorithm}")

    # 2. 尝试解码JWT（从token头部可以看出算法是HS512）
    try:
        print("\n--- 步骤1: 解码JWT ---")
        # 先解码header查看算法
        import base64
        import json
        header_b64 = token.split('.')[0]
        # 添加padding
        header_b64 += '=' * (4 - len(header_b64) % 4)
        header = json.loads(base64.b64decode(header_b64))
        print(f"JWT Header: {header}")

        # 使用正确的算法解码
        payload = jwt.decode(
            token,
            iot_adapter.secret_key,
            algorithms=[header['alg']],  # 使用token中的算法
            options={'verify_exp': False}  # 暂时不验证过期时间
        )
        print(f"JWT载荷: {payload}")

        # 3. 检查Redis中的token
        print("\n--- 步骤2: 检查Redis ---")
        session_id = payload.get('login_user_key', '')
        redis_key = f"login_tokens:{session_id}"
        print(f"Redis Key: {redis_key}")

        cached_token = await redis_client.get(redis_key)
        print(f"Redis中的token: {bool(cached_token)}")
        if cached_token:
            print(f"缓存token长度: {len(cached_token)}")
            print(f"Token匹配: {cached_token == token}")

        # 4. 尝试完整的IoT认证流程
        print("\n--- 步骤3: 完整认证流程 ---")
        try:
            user_info = await iot_adapter.authenticate_iot_token(token)
            print(f"认证成功: {user_info.username}")
        except Exception as e:
            print(f"认证失败: {e}")
            import traceback
            traceback.print_exc()

    except Exception as e:
        print(f"JWT解码失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_jwt_token())
