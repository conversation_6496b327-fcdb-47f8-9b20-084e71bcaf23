#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将LoginUser数据存储到Redis中，模拟Java系统的登录过程
"""

import redis
import json

# Redis配置
REDIS_CONFIG = {
    'host': '127.0.0.1',
    'port': 5862,
    'password': 'tldiot',
    'db': 1,
    'decode_responses': True
}

# 您提供的LoginUser数据
login_user_data = {
    "@type": "com.fastbee.common.core.domain.model.LoginUser",
    "browser": "Chrome 13",
    "deptId": 103,
    "expireTime": 1754366177106,
    "ipaddr": "*************",
    "loginLocation": "内网IP",
    "loginTime": 1754279777106,
    "neverExpire": False,
    "os": "Windows 10",
    "permissions": ["*:*:*"],
    "token": "457b5f2c-6482-47c3-b22c-0cf6a5cf3508",
    "user": {
        "admin": True,
        "avatar": "",
        "createBy": "admin",
        "createTime": "2021-12-15 21:36:18",
        "delFlag": "0",
        "dept": {
            "ancestors": "0,100,101",
            "children": [],
            "deptId": 103,
            "deptName": "研发部门",
            "leader": "物美",
            "orderNum": 1,
            "params": {"@type": "java.util.HashMap"},
            "parentId": 101,
            "status": "1"
        },
        "deptId": 103,
        "email": "<EMAIL>",
        "loginDate": "2025-08-04 11:54:03",
        "loginIp": "*************",
        "nickName": "蜂信管理员",
        "params": {"@type": "java.util.HashMap"},
        "password": "$2a$10$VJgxhCwmqjO69RXPtQPbxu8YIJ3rdA89004FVJf3Z9tKJxRGjQ4Nu",
        "phonenumber": "15888888888",
        "remark": "管理员",
        "roles": [{
            "admin": True,
            "dataScope": "1",
            "deptCheckStrictly": False,
            "flag": False,
            "menuCheckStrictly": False,
            "params": {"@type": "java.util.HashMap"},
            "roleId": 1,
            "roleKey": "admin",
            "roleName": "超级管理员",
            "roleSort": 1,
            "status": "0"
        }],
        "sex": "0",
        "status": "0",
        "userId": 1,
        "userName": "admin"
    },
    "userId": 1,
    "username": "admin"
}

def main():
    print("=" * 60)
    print("存储LoginUser数据到Redis")
    print("=" * 60)
    
    try:
        # 连接Redis
        r = redis.Redis(**REDIS_CONFIG)
        r.ping()
        print("✅ Redis连接成功")
        
        # 获取token
        token = login_user_data["token"]
        print(f"🔑 Token: {token}")
        
        # 尝试多种可能的Redis key格式
        possible_keys = [
            f"login_tokens:{token}",
            "login_tokens",
            token,
            f"token:{token}"
        ]
        
        # 将数据序列化为JSON
        json_data = json.dumps(login_user_data)
        print(f"📄 数据长度: {len(json_data)} 字符")
        
        # 存储到所有可能的key中
        for key in possible_keys:
            r.set(key, json_data, ex=86400)  # 24小时过期
            print(f"✅ 存储到 Redis key: {key}")
        
        print("\n🎯 验证存储结果:")
        for key in possible_keys:
            stored_data = r.get(key)
            if stored_data:
                print(f"✅ {key}: 存储成功 ({len(stored_data)} 字符)")
            else:
                print(f"❌ {key}: 存储失败")
        
        print("\n🚀 现在可以测试JWT认证了!")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
