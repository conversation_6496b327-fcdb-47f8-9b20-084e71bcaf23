#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import jwt

# 从您提供的LoginUser数据中提取的token
token = "457b5f2c-6482-47c3-b22c-0cf6a5cf3508"

# JWT配置
secret = "abcdefghijklfastbeesmartrstuvwxyz"
payload = {"login_user_key": token}

# 生成JWT
jwt_token = jwt.encode(payload, secret, algorithm="HS512")

print("🔑 Session Token:", token)
print("🎫 JWT Token:", jwt_token)
print()
print("📋 测试命令:")
print(f'curl -X GET "http://localhost:8000/api/v1/iot/kb/datasets" \\')
print(f'  -H "Authorization: Bearer {jwt_token}" \\')
print(f'  -H "Content-Type: application/json"')
