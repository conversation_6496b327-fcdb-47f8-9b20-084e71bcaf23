#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试IoT适配器的状态和配置
"""

import asyncio
from backend.common.security.iot_adapter import iot_adapter
from backend.core.conf import settings

async def test_iot_adapter():
    print("=" * 60)
    print("测试IoT适配器状态")
    print("=" * 60)
    
    # 检查配置
    print("📋 IoT配置:")
    print(f"  IOT_INTEGRATION_ENABLED: {settings.IOT_INTEGRATION_ENABLED}")
    print(f"  IOT_JWT_SECRET_KEY: {settings.IOT_JWT_SECRET_KEY}")
    print(f"  IOT_JWT_ALGORITHM: {settings.IOT_JWT_ALGORITHM}")
    print(f"  IOT_JWT_ISSUER: {settings.IOT_JWT_ISSUER}")
    print(f"  IOT_TOKEN_PREFIX: {settings.IOT_TOKEN_PREFIX}")
    print(f"  IOT_DEBUG_MODE: {settings.IOT_DEBUG_MODE}")
    
    # 检查适配器状态
    print(f"\n🔧 适配器状态:")
    print(f"  is_enabled(): {iot_adapter.is_enabled()}")
    
    # 测试JWT解码
    test_token = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjQ1N2I1ZjJjLTY0ODItNDdjMy1iMjJjLTBjZjZhNWNmMzUwOCJ9.YEfuSy_rl2g4_9rbSd4A34qtatSWhOBfMe-Rzw0c3McGJQjMBIUs_9VWxEx8inrDU5asAtVTYTyl0m6bVmYfzw"
    
    print(f"\n🎫 测试JWT解码:")
    print(f"  Token: {test_token[:50]}...")
    
    try:
        payload = iot_adapter.decode_iot_jwt(test_token)
        print(f"  ✅ 解码成功: {payload}")
        
        # 测试Redis验证
        print(f"\n🔍 测试Redis验证:")
        is_valid = await iot_adapter.verify_iot_token_in_redis(payload)
        print(f"  Redis验证结果: {is_valid}")
        
        # 测试完整认证
        print(f"\n🚀 测试完整认证:")
        try:
            user = await iot_adapter.authenticate_iot_token(test_token)
            print(f"  ✅ 认证成功: {user.username}")
        except Exception as e:
            print(f"  ❌ 认证失败: {e}")
            
    except Exception as e:
        print(f"  ❌ JWT解码失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_iot_adapter())
