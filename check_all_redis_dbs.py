#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有Redis数据库中的login token
"""

import redis
import json
from datetime import datetime

# Redis配置
REDIS_CONFIG = {
    'host': '127.0.0.1',
    'port': 5862,
    'password': 'tldiot',
    'decode_responses': True
}

def check_database(db_num):
    """检查指定数据库"""
    try:
        config = REDIS_CONFIG.copy()
        config['db'] = db_num
        r = redis.Redis(**config)
        r.ping()
        
        # 获取所有keys
        all_keys = r.keys("*")
        print(f"\n📊 数据库 {db_num}: {len(all_keys)} 个keys")
        
        if not all_keys:
            return []
        
        # 查找包含特定内容的keys
        login_related = []
        token_related = []
        user_related = []
        
        for key in all_keys:
            try:
                value = r.get(key)
                if value:
                    # 检查key名称
                    key_lower = key.lower()
                    if any(word in key_lower for word in ['login', 'token', 'user', 'session']):
                        print(f"  🔑 Key名称相关: {key}")
                        login_related.append(key)
                    
                    # 检查value内容
                    value_lower = value.lower()
                    if any(word in value_lower for word in ['loginuser', 'token', 'admin', '457b5f2c']):
                        print(f"  📄 内容相关: {key}")
                        if key not in login_related:
                            login_related.append(key)
                        
                        # 显示内容预览
                        preview = value[:100] + "..." if len(value) > 100 else value
                        print(f"      预览: {preview}")
                        
            except Exception as e:
                print(f"  ⚠️ 读取key失败 {key}: {e}")
        
        return login_related
        
    except Exception as e:
        print(f"❌ 检查数据库 {db_num} 失败: {e}")
        return []

def generate_jwt_for_session(session_id):
    """为session_id生成JWT token"""
    import jwt
    
    JWT_SECRET = "abcdefghijklfastbeesmartrstuvwxyz"
    JWT_ALGORITHM = "HS512"
    
    try:
        payload = {"login_user_key": session_id}
        token = jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)
        return token
    except Exception as e:
        print(f"❌ 生成JWT失败: {e}")
        return None

def main():
    print("=" * 60)
    print("检查所有Redis数据库中的login token")
    print("=" * 60)
    
    # 检查数据库0-15
    all_login_data = []
    
    for db_num in range(16):
        try:
            login_keys = check_database(db_num)
            if login_keys:
                all_login_data.extend([(db_num, key) for key in login_keys])
        except Exception as e:
            print(f"跳过数据库 {db_num}: {e}")
    
    if not all_login_data:
        print("\n❌ 在所有数据库中都未找到login相关数据")
        print("\n💡 建议:")
        print("1. 确认Java系统是否正在运行")
        print("2. 在Java系统中重新登录")
        print("3. 检查Redis配置是否正确")
        return
    
    print(f"\n🎯 找到 {len(all_login_data)} 个相关数据:")
    print("=" * 60)
    
    # 详细检查每个相关的key
    for db_num, key in all_login_data:
        try:
            config = REDIS_CONFIG.copy()
            config['db'] = db_num
            r = redis.Redis(**config)
            
            value = r.get(key)
            print(f"\n📋 数据库 {db_num}, Key: {key}")
            print(f"📄 长度: {len(value)} 字符")
            
            # 尝试解析JSON
            try:
                data = json.loads(value)
                if isinstance(data, dict):
                    # 查找token相关字段
                    for field_key, field_value in data.items():
                        if 'token' in field_key.lower() or field_key in ['login_user_key']:
                            print(f"  🔑 {field_key}: {field_value}")
                            
                            # 生成对应的JWT
                            jwt_token = generate_jwt_for_session(str(field_value))
                            if jwt_token:
                                print(f"  🎫 JWT: {jwt_token}")
                    
                    # 查找用户信息
                    if 'username' in data:
                        print(f"  👤 用户: {data['username']}")
                    if 'expireTime' in data:
                        expire_time = data['expireTime']
                        if expire_time:
                            expire_dt = datetime.fromtimestamp(expire_time / 1000)
                            print(f"  ⏰ 过期: {expire_dt.strftime('%Y-%m-%d %H:%M:%S')}")
                            
            except json.JSONDecodeError:
                # 不是JSON，直接显示内容
                print(f"  📄 内容: {value}")
                
                # 如果内容看起来像token，尝试生成JWT
                if len(value) == 36 and '-' in value:  # UUID格式
                    jwt_token = generate_jwt_for_session(value)
                    if jwt_token:
                        print(f"  🎫 JWT: {jwt_token}")
                        
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
    
    print("\n" + "=" * 60)
    print("🚀 如果找到了JWT token，请使用以下命令测试:")
    print('curl -X GET "http://localhost:8000/api/v1/iot/kb/datasets" \\')
    print('  -H "Authorization: Bearer <JWT_TOKEN>" \\')
    print('  -H "Content-Type: application/json"')

if __name__ == "__main__":
    main()
